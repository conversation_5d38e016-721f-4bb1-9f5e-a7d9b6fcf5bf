<script setup>
import { useUpdaterStore } from '@renderer/store'
import { ref, inject, onUnmounted } from 'vue'
import DownloadProgressDialog from './DownloadProgressDialog.vue'

const $root = inject('$root')
const $alert = inject('alert')
const $confirm = inject('confirm')
const updaterStore = useUpdaterStore()
const downloadProgressDialog = ref(null)
let initialized = false

function init() {
  if (initialized === false) {
    initialized = true
    window.electron.ipcRenderer.on('updater-message', updaterMassage)
  }
}

function updaterMassage(event, arg) {

  switch (arg.cmd) {
    case "update-error":
      updaterStore.reset()
      updaterStore.markError(arg.message)

      if (!updaterStore.isBackground) alert(arg.message)

      break;
    case "checking-for-update":
      updaterStore.visible(true)

      break;
    case "update-available":
      if (updaterStore.isBackground) {
        updaterStore.pause()

        $root.updaterDownload()
      } else {

        $confirm('Found version ' + arg.updateInfo.version + '!<br/>Download now?', 'Warning', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }).then(() => {
          downloadProgressDialog.value.show()

          $root.updaterDownload()
        }).catch(() => {
          updaterStore.visible(false)
        });      
      }

      break;
    case "update-not-available":
      updaterStore.visible(false)
      
      if (!updaterStore.isBackground) {
        $alert('There are currently no updates available.', 'Update')
      }

      break;
    case "download-progress":
      const progressObj = arg.progressObj

      updaterStore.updateProgress(progressObj.percent)

      downloadProgressDialog.value.update(progressObj.percent)

      break;
    case "update-downloaded":
      updaterStore.visible(false)
      
      if (updaterStore.isBackground) {
        $root.updateNow()
      } else {
        downloadProgressDialog.value.hide()

        $confirm('New version has been downloaded, update now?', 'Warning', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(() => {
          $root.updateNow()
        }).catch(() => {

        });
      }

      break;
    default:

  }
}

onUnmounted(() => {
  if (initialized) {
    window.electron.ipcRenderer.removeListener('updater-message', updaterMassage)
  }
})

defineExpose({
  init
})
</script>

<template>
  <el-icon v-if="useUpdaterStore().isError">
    <WarnTriangleFilled />
  </el-icon>
  <el-icon class="is-loading">
    <Refresh v-if="useUpdaterStore().isVisible" />
  </el-icon>
  <span v-if="useUpdaterStore().isVisible">
    {{ useUpdaterStore().progressPercent() }}
  </span>

  <DownloadProgressDialog ref="downloadProgressDialog" />
</template>
