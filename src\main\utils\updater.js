import { app, ipcMain } from 'electron'
import { platform } from '@electron-toolkit/utils'
import { exec, spawn } from 'child_process'
import { promisify } from 'util'
import pkg from 'electron-updater'

const { autoUpdater } = pkg
let cachedUpdateInfo = null;
const execAsync = promisify(exec);

let mainWin = null

function isLikelyDebInstall() {
  const execPath = process.execPath;
  const systemPaths = ['/opt', '/usr', '/snap'];

  return platform.isLinux && systemPaths.some((p) => execPath.startsWith(p));
}

// Custom install logic
async function customInstallAndRelaunch() {
  try {
    if (!cachedUpdateInfo || !cachedUpdateInfo.downloadedFile) {
      throw new Error('No update file available for installation');
    }

    const debPath = cachedUpdateInfo.downloadedFile; // Path to the downloaded .deb

    // Verify the file exists
    if (!require('fs').existsSync(debPath)) {
      throw new Error(`Update file not found: ${debPath}`);
    }

    // Check if sudo is available
    try {
      await execAsync('which sudo');
    } catch (err) {
      throw new Error('sudo command not available - cannot install system package');
    }

    const cmd = `sudo dpkg -i "${debPath}"`;
    console.log('Running install command:', cmd);

    // Add timeout to prevent hanging
    const installPromise = execAsync(cmd);
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Installation timeout')), 60000)
    );

    await Promise.race([installPromise, timeoutPromise]);

    // Optional: relaunch the app if needed
    const newAppPath = process.execPath; // Same binary path post-update

    console.log('Relaunch:', newAppPath);

    // Add error handling for spawn with timeout
    try {
      const child = spawn(newAppPath, [], {
        detached: true,
        stdio: 'ignore',
      });

      child.unref();

      // Give the new process time to start
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (spawnErr) {
      console.error('Failed to spawn new process:', spawnErr);
      // Continue with quit anyway
    }

    console.log('Quit');

    // Clean up the downloaded file
    try {
      require('fs').unlinkSync(debPath);
    } catch (cleanupErr) {
      console.warn('Failed to clean up downloaded file:', cleanupErr);
    }

    app.quit();
  } catch (err) {
    console.error('Custom install failed:', err);

    // Send error message to renderer
    sendUpdateMessage({
      cmd: 'update-error',
      message: `Installation failed: ${err.message}`
    });
  }
}

// Apply conditional override
if (isLikelyDebInstall()) {
  autoUpdater.quitAndInstall = async () => {
    await customInstallAndRelaunch()
  };
}

function sendUpdateMessage(data) {
  if (mainWin !== null)  mainWin.send('updater-message', data)
}

export function updaterHandle(win, dev) {
  mainWin = win.webContents

  // Clean up any existing listeners
  autoUpdater.removeAllListeners()
  
  if (dev) autoUpdater.forceDevUpdateConfig = true
  autoUpdater.allowDowngrade = true
  autoUpdater.disableWebInstaller = true
  autoUpdater.autoInstallOnAppQuit = false
  autoUpdater.disableDifferentialDownload = true
  autoUpdater.autoDownload = false;

  autoUpdater.on('error', function (message) {
    sendUpdateMessage({
      cmd: 'update-error',
      message: message
    })
  })

  autoUpdater.on('checking-for-update', function () {
    sendUpdateMessage({
      cmd: 'checking-for-update'
    })
  })

  autoUpdater.on('update-available', function (updateInfo) {
    sendUpdateMessage({
      cmd: 'update-available',
      updateInfo: updateInfo
    })
  })

  autoUpdater.on('update-not-available', function (updateInfo) {
    sendUpdateMessage({
      cmd: 'update-not-available',
      updateInfo: updateInfo
    })
  })

  autoUpdater.on('download-progress', function (progressObj) {
    sendUpdateMessage({
      cmd: 'download-progress',
      progressObj: progressObj
    })
  })

  autoUpdater.on(
    'update-downloaded',
    function (event, releaseNotes, releaseName, releaseDate, updateUrl) {

      if (isLikelyDebInstall()) {
        cachedUpdateInfo = event;
        console.log('Update downloaded:', cachedUpdateInfo.downloadedFile);
      }

      sendUpdateMessage({
        cmd: 'update-downloaded',
        message: {
          releaseNotes,
          releaseName,
          releaseDate,
          updateUrl
        }
      })
    }
  )
  
  ipcMain.on('update-set-feed-url', (ev, feedUrl) => {
    autoUpdater.setFeedURL(feedUrl)
  })

  ipcMain.on('update-check', () => {
    autoUpdater.checkForUpdates()
  })

  ipcMain.on('update-download', () => {
    autoUpdater.downloadUpdate()
  })

  ipcMain.on('update-install', () => {
    autoUpdater.quitAndInstall()
  })
}

// Export cleanup function
export function cleanupUpdater() {
  if (mainWin) {
    mainWin = null
  }

  // Clear cached update info
  cachedUpdateInfo = null

  // Remove all auto updater listeners
  autoUpdater.removeAllListeners()
}
