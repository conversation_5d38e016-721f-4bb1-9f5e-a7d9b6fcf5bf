import { ipcMain } from 'electron'
import Store from 'electron-store'

let storeInstance = null

export function storeHandle() {
  Store.initRenderer()

  storeInstance = new Store({
    migrations: {
      '>=1.1.1': (store) => {
        let settings = store.get('settings')

        store.set('settings.api.https', false)
        store.set('settings.clock.enabled', false)
        store.set('settings.autoupdate.enabled', true)        
        store.set('settings.autoupdate.url', 'https://www.g-jed.com/downloads/pricechecker-desktop/')
        store.set('settings.autoupdate.channel', 'stable')

        if (settings) {
          if (settings['host']) {
            store.set('settings.api.host', settings.host)

            store.delete('settings.host')
          }

          if (settings['app_path']) {
            store.set('settings.api.path', settings.app_path)

            store.delete('settings.app_path')
          }        

          if (settings['autoupdate']['enable']) {
            store.delete('settings.autoupdate.enable')
          }
        }
      }
    }
  })

  ipcMain.on('save-settings', (event, config) => {
    if (!storeInstance) {
      console.error('Store instance not initialized')
      event.returnValue = null
      return
    }

    try {
      storeInstance.set('settings', config.settings)
      event.returnValue = storeInstance.get('settings')
    } catch (error) {
      console.error('Error saving settings:', error)
      event.returnValue = null
    }
  })

  ipcMain.on('load-settings', (event) => {
    if (!storeInstance) {
      console.error('Store instance not initialized')
      event.returnValue = null
      return
    }

    try {
      event.returnValue = storeInstance.get('settings')
    } catch (error) {
      console.error('Error loading settings:', error)
      event.returnValue = null
    }
  })

  ipcMain.on('open-settings', () => {
    if (!storeInstance) {
      console.error('Store instance not initialized')
      return
    }

    try {
      storeInstance.openInEditor()
    } catch (error) {
      console.error('Error opening settings editor:', error)
    }
  })
}

export function cleanupStore() {
  if (storeInstance) {
    storeInstance = null
  }
}