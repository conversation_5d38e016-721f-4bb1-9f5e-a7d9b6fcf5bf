import { createApp } from 'vue/dist/vue.esm-bundler'
import App from './App.vue'
import { createPinia } from 'pinia'
import router from './router'
import mousetrap from 'mousetrap'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'

const store = createPinia()
const app = createApp(App)

app.use(router).use(store).use(ElementPlus)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.provide('mousetrap', mousetrap)
app.provide('msgbox', app.config.globalProperties.$msgbox)
app.provide('messageBox', app.config.globalProperties.$messageBox)
app.provide('alert', app.config.globalProperties.$alert)
app.provide('confirm', app.config.globalProperties.$confirm)
app.provide('prompt', app.config.globalProperties.$prompt)
app.provide('loading', app.config.globalProperties.$loading)
app.mount('#app')
