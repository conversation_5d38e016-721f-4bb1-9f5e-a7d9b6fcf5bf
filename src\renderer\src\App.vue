<script setup>
import NavMenu from './components/NavMenu.vue'
import { useRouter } from 'vue-router'
import * as shutdown from 'electron-shutdown-command'
import { useAppInfoStore,  useConfigStore, useClockStore, useUpdaterStore } from '@renderer/store'
import { getCurrentInstance, ref, onMounted, onBeforeUnmount, provide, inject } from 'vue'

const instance = getCurrentInstance()
const router = useRouter()

const appInfoStore = useAppInfoStore()
const configStore = useConfigStore()
const clockStore = useClockStore()
const updaterStore = useUpdaterStore()

const navMenu = ref(null)

onMounted(async () => {  
  await $root.loadSettings()
  await $root.appInfo()
  navMenu.value.init()

  // window.electron.ipcRenderer.send('process-message', 'Test Message')

  // window.electron.ipcRenderer.on('processed-message', (event, data) => {
  //   console.log(data)
  // })  
})

onBeforeUnmount(() => {
  stopAutoUpdate()
  // Add cleanup for clock store
  clockStore.stop()
})

function startAutoUpdate() {
  updaterStore.start()
}

function stopAutoUpdate() {  
  updaterStore.stop()
}

const $root = {
  updaterSetFeedURL(url) {
    window.electron.ipcRenderer.send('update-set-feed-url', url)
  },
  updaterCheck() {
    window.electron.ipcRenderer.send('update-check')
  },
  updaterDownload() {
    window.electron.ipcRenderer.send('update-download')
  },
  updateNow() {
    window.electron.ipcRenderer.send('update-install')
  },
  async appInfo() {
    appInfoStore.set(await window.electron.ipcRenderer.invoke('app-info'))
  },
  appRestart() {
    window.electron.ipcRenderer.send('app-restart')
  },
  appClose() {
    window.electron.ipcRenderer.send('app-close')
  },
  appKiosk(flag) {
    window.electron.ipcRenderer.send('app-kiosk', flag)
  },
  pcShutDown() {
    shutdown.shutdown()
  },
  pcReboot() {
    shutdown.reboot()
  },
  async saveSettings(settings) {
    let newSettings = JSON.parse(JSON.stringify(settings))
    await window.electron.ipcRenderer.sendSync('save-settings', newSettings)
  },
  async loadSettings() {
    let settings = await window.electron.ipcRenderer.sendSync('load-settings')

    configStore.set(settings)

    if (configStore.clock.enabled) clockStore.start()
    else clockStore.stop()

    $root.updaterSetFeedURL(configStore.updaterUrl())

    updaterStore.init({
      enabled: configStore.autoupdate.enabled,
      interval: configStore.autoupdate.interval,
      callback: $root.updaterCheck
    })
  },
  openSettings() {
    window.electron.ipcRenderer.send('open-settings')
  },
  goToPage(url) {
    router.push({
      path: url
    })
  }
}

provide('$root', $root)
</script>

<template>
  <div class="common-layout">
    <el-container>
      <el-header ref="controls">
        <NavMenu ref="navMenu" />
      </el-header>
      <el-main>
        <router-view @start-autoupdate="startAutoUpdate" @stop-autoupdate="stopAutoUpdate" />
      </el-main>
    </el-container>
  </div>  
</template>

<style lang="stylus">
body
  margin 0

#app
  font-family Avenir, Helvetica, Arial, sans-serif
  -webkit-font-smoothing antialiased
  -moz-osx-font-smoothing grayscale
  color #2c3e50

.el-header
  padding 0 !important
</style>
