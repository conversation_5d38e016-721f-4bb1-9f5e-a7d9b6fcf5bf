<script setup>
import NavMenu from './components/NavMenu.vue'
import { useRouter } from 'vue-router'
import * as shutdown from 'electron-shutdown-command'
import { useAppInfoStore,  useConfigStore, useClockStore, useUpdaterStore } from '@renderer/store'
import { getCurrentInstance, ref, onMounted, onBeforeUnmount, provide, inject } from 'vue'

const instance = getCurrentInstance()
const router = useRouter()

const appInfoStore = useAppInfoStore()
const configStore = useConfigStore()
const clockStore = useClockStore()
const updaterStore = useUpdaterStore()

const navMenu = ref(null)

// Store cleanup functions for event listeners
let updaterMessageCleanup = null
let processedMessageCleanup = null

onMounted(async () => {
  await $root.loadSettings()
  await $root.appInfo()
  navMenu.value.init()

  // Set up event listeners with cleanup functions
  if (window.api.onUpdaterMessage) {
    updaterMessageCleanup = window.api.onUpdaterMessage((data) => {
      // Handle updater messages
      updaterStore.handleMessage(data)
    })
  }

  if (window.api.onProcessedMessage) {
    processedMessageCleanup = window.api.onProcessedMessage((data) => {
      console.log('Processed message:', data)
    })
  }
})

onBeforeUnmount(() => {
  stopAutoUpdate()

  // Clean up clock store
  clockStore.stop()

  // Clean up updater store
  updaterStore.cleanup()

  // Clean up event listeners
  if (updaterMessageCleanup) {
    updaterMessageCleanup()
    updaterMessageCleanup = null
  }

  if (processedMessageCleanup) {
    processedMessageCleanup()
    processedMessageCleanup = null
  }
})

function startAutoUpdate() {
  updaterStore.start()
}

function stopAutoUpdate() {  
  updaterStore.stop()
}

const $root = {
  updaterSetFeedURL(url) {
    window.api.updateSetFeedURL(url)
  },
  updaterCheck() {
    window.api.updateCheck()
  },
  updaterDownload() {
    window.api.updateDownload()
  },
  updateNow() {
    window.api.updateInstall()
  },
  async appInfo() {
    try {
      const info = await window.api.appInfo()
      if (info) {
        appInfoStore.set(info)
      }
    } catch (error) {
      console.error('Error getting app info:', error)
    }
  },
  appRestart() {
    window.api.appRestart()
  },
  appClose() {
    window.api.appClose()
  },
  appKiosk(flag) {
    window.api.appKiosk(flag)
  },
  pcShutDown() {
    shutdown.shutdown()
  },
  pcReboot() {
    shutdown.reboot()
  },
  async saveSettings(settings) {
    try {
      let newSettings = JSON.parse(JSON.stringify(settings))
      const result = await window.api.saveSettings(newSettings)
      return result
    } catch (error) {
      console.error('Error saving settings:', error)
      return null
    }
  },
  async loadSettings() {
    try {
      let settings = await window.api.loadSettings()

      if (settings) {
        configStore.set(settings)

        if (configStore.clock.enabled) clockStore.start()
        else clockStore.stop()

        $root.updaterSetFeedURL(configStore.updaterUrl())

        updaterStore.init({
          enabled: configStore.autoupdate.enabled,
          interval: configStore.autoupdate.interval,
          callback: $root.updaterCheck
        })
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  },
  openSettings() {
    window.api.openSettings()
  },
  goToPage(url) {
    router.push({
      path: url
    })
  }
}

provide('$root', $root)
</script>

<template>
  <div class="common-layout">
    <el-container>
      <el-header ref="controls">
        <NavMenu ref="navMenu" />
      </el-header>
      <el-main>
        <router-view @start-autoupdate="startAutoUpdate" @stop-autoupdate="stopAutoUpdate" />
      </el-main>
    </el-container>
  </div>  
</template>

<style lang="stylus">
body
  margin 0

#app
  font-family Avenir, Helvetica, Arial, sans-serif
  -webkit-font-smoothing antialiased
  -moz-osx-font-smoothing grayscale
  color #2c3e50

.el-header
  padding 0 !important
</style>
