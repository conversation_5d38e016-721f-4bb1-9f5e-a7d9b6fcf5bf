import { defineStore } from 'pinia'

export const useAppInfoStore = defineStore('AppInfo', {
  state: () => {
    return {     
      os: null, 
      node: null,
      chrome: null,
      electron: null,
      version: null,
      name: null,
      ip: null
    }
  },
  getters: {},
  actions: {
    raw() {
      return JSON.parse(JSON.stringify(this.$state))
    },
    set(config) {
      this.$state = this.deepMergeWithSpread(this.$state, config)
    },
    deepMergeWithSpread(obj1, obj2) {
      const result = { ...obj1 };
    
      for (let key in obj2) {
        if (obj2.hasOwnProperty(key)) {
          if (obj2[key] instanceof Object && obj1[key] instanceof Object) {
            result[key] = this.deepMergeWithSpread(obj1[key], obj2[key]);
          } else {
            result[key] = obj2[key];
          }
        }
      }
    
      return result;
    },
    ipToHTML() {
      let ip = '<br/><table border="1" style="width:100%"> '

      const json = JSON.parse(JSON.stringify(this.ip))

      for(var k in json) {
        ip += "<tr><td>" + k + "</td><td>" + json[k].join(',') + '</td></tr>'
      }

      ip += '</table>'

      return ip;
    }
  }
})
