import { defineStore } from 'pinia'

export const useConfigStore = defineStore('Config', {
  state: () => {
    return {
      autoupdate: {
        url: 'https://g-jed.com/downloads/pricechecker-desktop/',
        channel: 'stable',
        enabled: true,
        interval: 10
      },
      clock: {
        enabled: false,
      },
      api: {
        https: false,
        host: "",
        path: "PriceChecker/nuc.php"
      }
    }
  },
  getters: {},
  actions: {
    raw() {
      return JSON.parse(JSON.stringify(this.$state))
    },
    set(config) {
      this.$state = this.deepMergeWithSpread(this.$state, config)
    },
    deepMergeWithSpread(obj1, obj2) {
      const result = { ...obj1 };
    
      for (let key in obj2) {
        if (obj2.hasOwnProperty(key)) {
          if (obj2[key] instanceof Object && obj1[key] instanceof Object) {
            result[key] = this.deepMergeWithSpread(obj1[key], obj2[key]);
          } else {
            result[key] = obj2[key];
          }
        }
      }
    
      return result;
    },
    updaterUrl() {
      return this.autoupdate.url + this.autoupdate.channel + '/'
    },
    url() {
      if (this.api.host === "") return null

      return (this.api.https ? 'https://' : 'http://') + this.api.host + '/' + this.api.path
    }
  }
})
