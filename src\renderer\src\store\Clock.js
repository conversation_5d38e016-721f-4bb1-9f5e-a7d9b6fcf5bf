import { defineStore } from 'pinia'
import { <PERSON>ronJob } from 'cron'

export const useClockStore = defineStore('Clock', {
  state: () => {
    return {
      enabled: false,
      time: null,
      cron: null
    }
  },
  getters: {},
  actions: {
    start(){
      this.stop()
      
      this.enabled = true
      // Fix: Use arrow function to preserve 'this' context
      this.cron = new CronJob(
        '* * * * * *',
        () => {
          this.tick()  // ✅ Uses existing store instance
        },
        null,
        true,
        'Asia/Kuala_Lumpur'
      )
    },
    stop() {
      if (this.cron) {
        this.cron.stop()
        this.cron.destroy() // ✅ Properly destroy cron job
        this.cron = null
      }

      this.enabled = false      
    },
    tick() {
      this.time = Intl.DateTimeFormat(navigator.language, {
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric'
      }).format()
    }
  }
})
