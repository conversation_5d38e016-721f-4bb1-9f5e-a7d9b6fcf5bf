import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// Custom APIs for renderer
const api = {
  // App control APIs
  appInfo: () => ipcRenderer.invoke('app-info'),
  appVersion: () => ipcRenderer.invoke('app-version'),
  appRestart: () => ipcRenderer.send('app-restart'),
  appClose: () => ipcRenderer.send('app-close'),
  appKiosk: (flag) => ipcRenderer.send('app-kiosk', flag),
  appFullscreen: (flag) => ipcRenderer.send('app-fullscreen', flag),

  // Settings APIs
  saveSettings: (config) => ipcRenderer.sendSync('save-settings', config),
  loadSettings: () => ipcRenderer.sendSync('load-settings'),
  openSettings: () => ipcRenderer.send('open-settings'),

  // Updater APIs
  updateSetFeedURL: (url) => ipcRenderer.send('update-set-feed-url', url),
  updateCheck: () => ipcRenderer.send('update-check'),
  updateDownload: () => ipcRenderer.send('update-download'),
  updateInstall: () => ipcRenderer.send('update-install'),

  // Event listeners
  onUpdaterMessage: (callback) => {
    const wrappedCallback = (event, data) => callback(data)
    ipcRenderer.on('updater-message', wrappedCallback)
    return () => ipcRenderer.removeListener('updater-message', wrappedCallback)
  },

  onProcessedMessage: (callback) => {
    const wrappedCallback = (event, data) => callback(data)
    ipcRenderer.on('processed-message', wrappedCallback)
    return () => ipcRenderer.removeListener('processed-message', wrappedCallback)
  },

  // Process message
  processMessage: (msg) => ipcRenderer.send('process-message', msg)
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  window.electron = electronAPI
  window.api = api
}
