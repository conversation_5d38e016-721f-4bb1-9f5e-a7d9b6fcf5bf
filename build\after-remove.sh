#!/bin/bash

# Loop through each user with a valid home directory in /home
for userdir in /home/<USER>
    if [ -d "$userdir" ]; then
        AUTOSTART_FILE="$userdir/.config/autostart/pricechecker.desktop"

        if [ -f "$AUTOSTART_FILE" ]; then
            echo "Removing autostart entry for $userdir"
            rm -f "$AUTOSTART_FILE"
        fi

        DESKTOPSHORTCUT_FILE="$userdir/Desktop/Price Checker.desktop"

        if [ -f "$DESKTOPSHORTCUT_FILE" ]; then
            echo "Removing desktop entry for $userdir"
            rm -f "$DESKTOPSHORTCUT_FILE"
        fi
    fi
done

exit 0