import https from 'https';
import fs from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';
import { join } from 'path'
import os from 'os';
import { app } from 'electron'
import * as shutdown from 'electron-shutdown-command'

const execAsync = promisify(exec);

/**
 * Download the .deb file
 */
export function downloadDeb() {
  const url = 'https://www.g-jed.com/downloads/pricechecker-desktop/migrate/pricechecker-desktop_1.1.1_amd64.deb';
  const outputPath = join(os.tmpdir(), 'pricechecker-desktop_1.1.1_amd64.deb');

  return new Promise((resolve, reject) => {
    // Clean up any existing file
    if (fs.existsSync(outputPath)) {
      try {
        fs.unlinkSync(outputPath);
      } catch (err) {
        console.warn('Failed to remove existing file:', err);
      }
    }

    const file = fs.createWriteStream(outputPath);

    // Add timeout for the download
    const timeout = setTimeout(() => {
      file.destroy();
      reject(new Error('Download timeout'));
    }, 300000); // 5 minutes timeout

    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        clearTimeout(timeout);
        file.destroy();
        return reject(new Error(`Failed to download: ${response.statusCode} ${response.statusMessage}`));
      }

      response.pipe(file);

      file.on('finish', () => {
        clearTimeout(timeout);
        file.close((err) => {
          if (err) {
            reject(err);
          } else {
            resolve(outputPath);
          }
        });
      });

      file.on('error', (err) => {
        clearTimeout(timeout);
        reject(err);
      });
    }).on('error', (err) => {
      clearTimeout(timeout);
      file.destroy();
      reject(err);
    });
  });
}

/**
 * Download and install the .deb file
 */
export async function migrateToDeb(version) {
  if (!fs.existsSync("/opt/Price Checker")) {
    try {
      const debPath = await downloadDeb(version);
      console.log(`Downloaded to ${debPath}`);

      const cmd = `sudo dpkg -i "${debPath}"`;
      await execAsync(cmd);

      console.log('Deb installed successfully.');

      const homedir = os.homedir();
      const autostartDir = join(homedir, '.config', 'autostart');
      const desktopDir = join(homedir, 'Desktop');
      const execPath = '/opt/Price Checker/pricechecker-desktop';

      const desktopEntry = `
[Desktop Entry]
Type=Application
Name=Price Checker
Exec="${execPath}"
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
Comment=Automatically start on login
Terminal=false
`;

      // Use consistent naming: pricechecker.desktop
      const autostartFile = join(autostartDir, 'pricechecker.desktop');
      const oldAutostartFile = join(autostartDir, 'exec.sh.desktop');
      const oldDesktopShortcut = join(desktopDir, 'PriceChecker.desktop');

      try {
        if (fs.existsSync(oldAutostartFile)) {
          fs.rmSync(oldAutostartFile)
        }

        if (fs.existsSync(oldDesktopShortcut)) {
          fs.rmSync(oldDesktopShortcut)
        }

        fs.mkdirSync(autostartDir, { recursive: true });
        fs.writeFileSync(autostartFile, desktopEntry.trim());
        fs.chmodSync(autostartFile, 0o755);

        console.log('✅ Autostart shortcut created.');
        shutdown.reboot()
        console.log('Migration completed. Rebooting.');
      } catch (err) {
        console.error('❌ Failed to create shortcuts:', err);
      }
    } catch (err) {
      console.error(err);
    }
  }
}
