<script setup>
import { ref } from 'vue'

const dialogVisible = ref(false)
const messageVisible = ref(true)
const progressVisible = ref(false)
const progress = ref(null)

function show() {
  messageVisible.value = true
  progressVisible.value = false
  progress.value = null
  dialogVisible.value = true
}

function hide() {
  dialogVisible.value = false  
}

function update(percent) {
  if (messageVisible) {
    messageVisible.value = false
    progressVisible.value = true
  }

  progress.value = Math.floor(percent)
}

defineExpose({
  show,
  hide,
  update
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="Update"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <span v-if="messageVisible">
      Checking for Update.
    </span>
    <el-progress 
      v-if="progressVisible" 
      :text-inside="true" 
      :stroke-width="20" 
      :percentage="progress" />    
  </el-dialog>
</template>
