{
	"folders": [
		{
			"path": "."
		}
	],
	"settings": {
		"editor.tabSize": 2,
		"editor.insertSpaces": true,
		"editor.formatOnSave": false,
		"editor.codeActionsOnSave": {
			"source.fixAll.eslint": "explicit"
		},
		"vue3snippets.enable-compile-vue-file-on-did-save-code": false,
		"javascript.format.enable": false,
		"eslint.alwaysShowStatus": true,
		"eslint.options": {
			"extensions": [
				".html",
				".js",
				".vue",
				".jsx"
			]
		},
		"eslint.validate": [
			"html",
			"vue",
			"javascript",
			"javascriptreact",
		],
		"vue3snippets.trailingComma": "all",
		"files.exclude": {
			"**/dist_electron": true,
			"**/shell-chrome": true
		}
	},
}