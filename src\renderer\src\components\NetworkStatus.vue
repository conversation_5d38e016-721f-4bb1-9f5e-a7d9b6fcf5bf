<template>
  <el-icon>
    <UploadFilled />
  </el-icon>
  <strong>
    {{ networkStatus }}
  </strong>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'

var networkStatus = ref('\xa0')

onMounted(() => {
  window.addEventListener('online', updateOnlineStatus)
  window.addEventListener('offline', updateOnlineStatus)
  updateOnlineStatus()
})

onBeforeUnmount(() => {
  window.removeEventListener('online', updateOnlineStatus)
  window.removeEventListener('offline', updateOnlineStatus)
})

function updateOnlineStatus() {
  networkStatus.value = navigator.onLine ? 'Online' : 'Offline'
}
</script>
