import { app, shell, BrowserWindow, ipcMain } from 'electron'
import fs from 'fs';
import { join } from 'path'
import { optimizer, is, platform } from '@electron-toolkit/utils'
import icon from '../../build/icons/icon.ico?asset'
import { storeHandle, updaterHandle, migrateToDeb, cleanupStore, cleanupUpdater } from './utils'
import * as os from 'os'
import { exec } from 'child_process'
import * as si from 'systeminformation'
// import installExtension, { VUEJS3_DEVTOOLS } from 'electron-devtools-installer'

const gotTheLock = app.requestSingleInstanceLock()
let win = null

function createWindow() {
  // Create the browser window.
  win = new BrowserWindow({
    width: 1024,
    height: 768,
    show: false,
    autoHideMenuBar: true,
    alwaysOnTop: !is.dev,
    kiosk: !is.dev,
    ...(platform.isLinux ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      webSecurity: true,
      webviewTag: true,
      nodeIntegration: false,
      contextIsolation: true
    },
    icon: icon
  })

  init_handle()

  win.on('ready-to-show', () => {
    win.setTitle(app.getName() + ' ' + app.getVersion())
    win.show()
  })

  win.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    win.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    win.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

if (!gotTheLock) {
  app.quit()
} else {
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    if (win) {
      if (win.isMinimized()) win.restore()
      win.focus()
    }
  })
  // This method will be called when Electron has finished
  // initialization and is ready to create browser windows.
  // Some APIs can only be used after this event occurs.
  app.whenReady().then(async () => {

    if (!is.dev && platform.isLinux && !process.env.APPIMAGE) {
      debInit()
    }

    // if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    //   installExtension(VUEJS3_DEVTOOLS, {
    //     loadExtensionOptions: {
    //       allowFileAccess: true
    //     }
    //   })
    //     .then((name) => console.log(`Added Extension:  ${name}`))
    //     .catch((err) => console.log('An error occurred: ', err))
    // }

    // Set app user model id for windows
    // electronApp.setAppUserModelId('com.electron')

    // Default open or close DevTools by F12 in development
    // and ignore CommandOrControl + R in production.
    // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
    app.on('browser-window-created', (_, window) => {
      optimizer.watchWindowShortcuts(window)
    })

    createWindow()

    app.on('activate', function () {
      // On macOS it's common to re-create a window in the app when the
      // dock icon is clicked and there are no other windows open.
      if (BrowserWindow.getAllWindows().length === 0) createWindow()
    })

    if (!is.dev && process.env.APPIMAGE != undefined && app.getVersion() == "1.1.1") {
      migrateToDeb();
    }
  })
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  // Clean up resources before quitting
  cleanupStore()
  cleanupUpdater()
  cleanupIpcHandlers()

  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Handle app quit event for additional cleanup
app.on('before-quit', () => {
  console.log('Application is quitting, cleaning up resources...')
  cleanupStore()
  cleanupUpdater()
  cleanupIpcHandlers()
})

// Clean up IPC handlers to prevent memory leaks
function cleanupIpcHandlers() {
  // Remove all IPC handlers
  ipcMain.removeAllListeners('app-info')
  ipcMain.removeAllListeners('app-version')
  ipcMain.removeAllListeners('app-restart')
  ipcMain.removeAllListeners('app-close')
  ipcMain.removeAllListeners('app-kiosk')
  ipcMain.removeAllListeners('app-fullscreen')
  ipcMain.removeAllListeners('process-message')
  ipcMain.removeAllListeners('save-settings')
  ipcMain.removeAllListeners('load-settings')
  ipcMain.removeAllListeners('open-settings')
  ipcMain.removeAllListeners('update-set-feed-url')
  ipcMain.removeAllListeners('update-check')
  ipcMain.removeAllListeners('update-download')
  ipcMain.removeAllListeners('update-install')
}

// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.

function init_handle() {
  updaterHandle(win, is.dev)
  storeHandle()

  ipcMain.handle('app-info', async () => {
    const nets = os.networkInterfaces();
    const results = Object.create(null); // Or just '{}', an empty object
    
    for (const name of Object.keys(nets)) {
      for (const net of nets[name]) {
        // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
        // 'IPv4' is in Node <= 17, from 18 it's a number 4 or 6
        const familyV4Value = typeof net.family === 'string' ? 'IPv4' : 4
        if (net.family === familyV4Value && !net.internal) {
          if (!results[name]) {
            results[name] = [];
          }
          results[name].push(net.address);
        }
      }
    }

    let osName = 'Failed to get OS info';

    try {
      const osInfo = await si.osInfo();
      osName = `${osInfo.distro} ${osInfo.release}`;
    } catch (error) {      
      // console.error('Failed to get OS info:', error);
    }

    return {
      os: osName,
      node: process.versions.node,
      chrome: process.versions.chrome,
      electron: process.versions.electron,
      version: app.getVersion(),
      name: app.getName(),
      ip: results
    }
  })

  ipcMain.handle('app-version', () => {
    return app.getVersion()
  })

  ipcMain.on('app-restart', () => {
    app.relaunch()
    app.exit(0)
  })

  ipcMain.on('app-close', () => {
    app.quit()
  })

  ipcMain.on('app-kiosk', (event, flag) => {
    win.setKiosk(flag)
  })

  ipcMain.on('app-fullscreen', (event, flag) => {
    win.setFullScreen(flag)
  })

  ipcMain.on('process-message', (event, msg) => {
    let new_message = 'Received: ' + msg
    win.webContents.send('processed-message', { msg: new_message })
  })
}

function debInit() {
  const homedir = os.homedir();
  const autostartDir = join(homedir, '.config', 'autostart');
  const desktopDir = join(homedir, 'Desktop');
  const execPath = process.execPath;
  const iconPath = join(process.resourcesPath, 'icon.png');
  const productName = app.getName()

  const desktopEntry = `
[Desktop Entry]
Type=Application
Name=${productName}
Exec="${execPath}"
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
Comment=Automatically start on login
Icon=${iconPath}
Terminal=false
`;

  // Use consistent naming: pricechecker.desktop
  const autostartFile = join(autostartDir, 'pricechecker.desktop');
  const desktopShortcut = join(desktopDir, productName + '.desktop');

  try {
    fs.mkdirSync(autostartDir, { recursive: true });
    fs.writeFileSync(autostartFile, desktopEntry.trim());
    fs.chmodSync(autostartFile, 0o755);

    fs.mkdirSync(desktopDir, { recursive: true });
    fs.writeFileSync(desktopShortcut, desktopEntry.trim());
    fs.chmodSync(desktopShortcut, 0o755);
    exec(`gio set "${desktopShortcut}" metadata::trusted true`);

    console.log('✅ Autostart and desktop shortcut created.');
  } catch (err) {
    console.error('❌ Failed to create shortcuts:', err);
  }
}
