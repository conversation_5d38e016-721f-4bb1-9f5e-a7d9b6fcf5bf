{"name": "pricechecker-desktop", "homepage": "https://www.ibtm.com.my", "description": "Price Checker", "author": "IBTM", "appId": "com.g-jed.pricechecker-desktop", "productName": "Price Checker", "guid": "b98adbf9-8ab2-54da-a25f-680a946942d3", "publish": {"provider": "generic", "url": "https://g-jed.com/downloads/pricechecker-desktop/stable/"}, "version": "0.0.0", "main": "./out/main/index.js", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --config electron-builder.config.mjs --dir", "build:win": "npm run build && electron-builder --config electron-builder.config.mjs --win", "build:mac": "npm run build && electron-builder --config electron-builder.config.mjs --mac", "build:linux": "npm run build && electron-builder --config electron-builder.config.mjs --linux", "build:icon": "electron-icon-builder -f --input=./src/assets/icon.png --output=./build/"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "cron": "^4.3.1", "electron-shutdown-command": "^2.0.1", "electron-updater": "^6.1.7", "pinia": "^3.0.3"}, "devDependencies": {"@electron-toolkit/eslint-config": "^2.1.0", "@element-plus/icons-vue": "^2.3.1", "@rushstack/eslint-patch": "^1.7.2", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "electron": "^37.2.0", "electron-builder": "^26.0.12", "electron-devtools-installer": "^4.0.0", "electron-icon-builder": "^2.0.1", "electron-store": "^10.0.0", "electron-vite": "^4.0.0", "element-plus": "^2.5.5", "eslint": "^9.9.0", "eslint-plugin-vue": "^10.2.0", "less": "^4.2.0", "mousetrap": "^1.6.5", "prettier": "^3.2.5", "stylus": "^0.64.0", "stylus-loader": "^8.1.0", "systeminformation": "^5.27.1", "vite": "^7.0.5", "vite-plugin-electron-renderer": "^0.14.5", "vue": "^3.4.15", "vue-router": "^4.2.5"}}