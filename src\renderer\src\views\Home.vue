<script setup>
import { getCurrentInstance, onMounted, onBeforeUnmount, inject, ref } from 'vue'
import { useConfigStore, useUpdaterStore } from '@renderer/store'

const instance = getCurrentInstance()
const $root = inject('$root')
const webview = ref(null)
const configStore = useConfigStore()
const updaterStore = useUpdaterStore()

const emits = defineEmits(['start-autoupdate', 'stop-autoupdate'])

onMounted(() => {
  if (configStore.url() == null) {
    $root.goToPage('/setting')
  } else {
    webview.value.addEventListener('did-finish-load', webviewFocus)
    webview.value.src = configStore.url()
    
    window.addEventListener('resize', doLayout)
    doLayout()

    // Only start if not already started
    if (!updaterStore.started) {
      emits('start-autoupdate')
    }
  }
})

onBeforeUnmount(() => {
  emits('stop-autoupdate')
  window.removeEventListener('resize', doLayout)
  
  // Clean up webview listener
  if (webview.value) {
    webview.value.removeEventListener('did-finish-load', webviewFocus)
  }
})

function doLayout() {
  const controls = instance.root.refs.controls

  if (controls) {
    const controlsHeight = controls.$el.clientHeight  
    const windowHeight = document.documentElement.clientHeight
    
    const webviewHeight = windowHeight - controlsHeight

    webview.value.style.height = webviewHeight + 'px'

    setTimeout(() => {
      const controls = instance.root.refs.controls

      if (controls) {
        const controlsHeight = controls.offsetHeight
        const windowHeight = document.documentElement.clientHeight

        const webviewHeight = windowHeight - controlsHeight

        if (webview.value && webview.value.offsetHeight != webviewHeight) {
          doLayout()
        }
      }
    }, 250)
  }
}

function webviewFocus() {
  webview.value.focus()
}
</script>

<template>
  <webview ref="webview" src="" />
</template>

<style lang="stylus">
webview
  position absolute
  top 60
  bottom 0
  left 0
  background-color #01209d
  width 100% 
  height 100%
</style>
