<script setup>
import { getCurrentInstance, onMounted, onBeforeUnmount, inject, ref } from 'vue'
import { useConfigStore, useUpdaterStore } from '@renderer/store'

const instance = getCurrentInstance()
const $root = inject('$root')
const webview = ref(null)
const configStore = useConfigStore()
const updaterStore = useUpdaterStore()

// Prevent infinite recursion in doLayout
let layoutInProgress = false
let layoutRetryCount = 0
const MAX_LAYOUT_RETRIES = 5

const emits = defineEmits(['start-autoupdate', 'stop-autoupdate'])

onMounted(() => {
  if (configStore.url() == null) {
    $root.goToPage('/setting')
  } else {
    webview.value.addEventListener('did-finish-load', webviewFocus)
    webview.value.src = configStore.url()
    
    window.addEventListener('resize', doLayout)
    doLayout()

    // Only start if not already started
    if (!updaterStore.started) {
      emits('start-autoupdate')
    }
  }
})

onBeforeUnmount(() => {
  emits('stop-autoupdate')
  window.removeEventListener('resize', doLayout)
  
  // Clean up webview listener
  if (webview.value) {
    webview.value.removeEventListener('did-finish-load', webviewFocus)
  }
})

function doLayout() {
  // Prevent infinite recursion
  if (layoutInProgress) {
    return
  }

  layoutInProgress = true

  try {
    const controls = instance.root.refs.controls

    if (controls && webview.value) {
      const controlsHeight = controls.$el?.clientHeight || controls.offsetHeight || 0
      const windowHeight = document.documentElement.clientHeight

      const webviewHeight = windowHeight - controlsHeight

      webview.value.style.height = webviewHeight + 'px'

      // Use requestAnimationFrame for better performance and to avoid recursion issues
      setTimeout(() => {
        try {
          const controls = instance.root.refs.controls

          if (controls && webview.value && layoutRetryCount < MAX_LAYOUT_RETRIES) {
            const currentControlsHeight = controls.offsetHeight || 0
            const currentWindowHeight = document.documentElement.clientHeight
            const expectedWebviewHeight = currentWindowHeight - currentControlsHeight

            if (Math.abs(webview.value.offsetHeight - expectedWebviewHeight) > 1) {
              layoutRetryCount++
              layoutInProgress = false
              doLayout()
            } else {
              layoutRetryCount = 0
            }
          } else {
            layoutRetryCount = 0
          }
        } catch (error) {
          console.error('Error in doLayout timeout:', error)
          layoutRetryCount = 0
        } finally {
          layoutInProgress = false
        }
      }, 250)
    }
  } catch (error) {
    console.error('Error in doLayout:', error)
  } finally {
    if (layoutInProgress) {
      layoutInProgress = false
    }
  }
}

function webviewFocus() {
  webview.value.focus()
}
</script>

<template>
  <webview ref="webview" src="" />
</template>

<style lang="stylus">
webview
  position absolute
  top 60
  bottom 0
  left 0
  background-color #01209d
  width 100% 
  height 100%
</style>
