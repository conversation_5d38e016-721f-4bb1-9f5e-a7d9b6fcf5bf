<script setup>
import AppVersion from './AppVersion.vue'
import NetworkStatus from './NetworkStatus.vue'
import Clock from './Clock.vue'
import DownloadStatus from './DownloadStatus.vue'
import { ref, inject } from 'vue'
import { useRouter } from 'vue-router'
import { useAppInfoStore, useConfigStore, useUpdaterStore } from '@renderer/store'

const router = useRouter()
const $alert = inject('alert')
const downloadStatus = ref(null)
const clock = ref(null)

const handleSelect = (index) => {   
  if (index == 1) {
    $alert("os: " + useAppInfoStore().os + "<br/>" +
      "node: " + useAppInfoStore().node + "<br/>" +
      "chrome: " + useAppInfoStore().chrome + "<br/>" +
      "electron: " + useAppInfoStore().electron + "<br/>" +
      "version: " + useAppInfoStore().version + "<br/>" +
      "ip: " + useAppInfoStore().ipToHTML(), "App Info", {
        dangerouslyUseHTMLString: true,
      }
    )
  } else if (index == 4) {
    if (useUpdaterStore().errorMessage) {
      $alert('<textarea style="width:400px; height:300px;">' + useUpdaterStore().errorMessage + '</textarea>', 
        "Updater Error", 
        { 
          dangerouslyUseHTMLString: true
        }
      )
    }
  } else {
    const resolved = router.resolve({ path: index })

    if (resolved.name != 'PageNotFound') {
      router.push({
        path: index
      })
    }
  }
}

function init() {
  downloadStatus.value.init()
}

function showCheckUpdate(show) {
  downloadStatus.value.showCheckUpdate(show)
}

function downloadProgress(percent) {
  downloadStatus.value.progress(percent)
}

defineExpose({
  init,
  downloadProgress,
  showCheckUpdate
})
</script>

<template>
  <el-menu
    default-active="1"
    mode="horizontal"
    background-color="#01209D"
    text-color="#fff"
    active-text-color="#fff"
    @select="handleSelect"
  >
    <el-menu-item index="1">
      <AppVersion />
    </el-menu-item>
    <el-menu-item index="2">
      <NetworkStatus />
    </el-menu-item>
    <el-menu-item index="3">
      <Clock ref="clock"/>
    </el-menu-item>
    <el-menu-item index="4">
      <DownloadStatus ref="downloadStatus" />
    </el-menu-item>
    <el-sub-menu index="5" class="dock-right">
      <template #title>
        <el-icon>
          <Setting />
        </el-icon>
        &nbsp;
      </template>
      <el-menu-item index="/">Home</el-menu-item>
      <el-menu-item index="/setting">Setting</el-menu-item>
    </el-sub-menu>
  </el-menu>
</template>

<style lang="stylus">
.el-menu--horizontal
  display block !important

.dock-right
  float right
  margin-right 10px !important

.el-menu--popup
  background #fff !important

.el-menu--popup > .el-menu-item
  background #fff !important
  color #000 !important

.el-menu--popup > .el-menu-item:hover
  background #fff !important
  color #000 !important
</style>
