<script setup>
import { useConfigStore } from '@renderer/store'
import { ref, reactive, onMounted, inject } from 'vue'
import { SwitchButton } from '@element-plus/icons-vue'
const emits = defineEmits(['start-autoupdate', 'stop-autoupdate'])

const configStore = useConfigStore()
const $root = inject('$root')
const $loading = inject('loading')
const $alert = inject('alert')
const $confirm = inject('confirm')
const form = ref(null)
const settings = reactive({
  autoupdate: {
    url: '',
    channel: 'stable',
    enabled: true,
    interval: 10
  },
  clock: {
    enabled: false,
  },
  api: {
    https: false,
    host: '',
    path: ''
  }
})
const rules = {
  autoupdate:{
    url: [
      {
        required: true,
        message: 'Please Enter Updater URL',
        trigger: 'blur'
      }
    ]
  },
  api: {
    host: [
      {
        required: true,
        message: 'Please Enter Host',
        trigger: 'blur'
      }
    ]
  },
  interval: [
    {
      required: true,
      message: 'Interval is required'
    },
    {
      type: 'number',
      min: 5,
      message: 'Interval should be at least 5 Minutes'
    }
  ]
}
const kiosk = ref(true)
const channel_options = [
  {
    value: 'stable',
    label: 'Stable',
  },
  {
    value: 'beta',
    label: 'Beta',
  }
]

var loadingInstance = null

onMounted(() => {
  settings.autoupdate.url = configStore.autoupdate.url
  settings.autoupdate.channel = configStore.autoupdate.channel
  settings.autoupdate.enabled = configStore.autoupdate.enabled
  settings.autoupdate.interval = configStore.autoupdate.interval
  settings.clock.enabled = configStore.clock.enabled
  settings.api.https = configStore.api.https
  settings.api.host = configStore.api.host
  settings.api.path = configStore.api.path
})

function onSubmit() {
  showLoading()
  
  try {
    form.value.validate((valid) => {
      if (valid) {
        try {
          $root.saveSettings({ settings: settings })

          $alert('Saved', {
            title: 'Setting',
            confirmButtonText: 'OK',
            callback: async () => {     
              location.reload();     
            }
          })
        } catch (err) {
          console.error('Save settings failed:', err);
          closeLoading();
        }
      } else {
        closeLoading()
        return false
      }
    })
  } catch (err) {
    console.error('Form validation failed:', err);
    closeLoading();
  }
}

function showLoading() {
  loadingInstance = $loading({
    lock: true,
    text: 'Loading',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  })
}

function closeLoading() {
  if (loadingInstance) loadingInstance.close()
}

function checkUpdate() {
  $root.updaterCheck()
}

function updateNow() {
  $root.updateNow()
}

function openConfig() {
  $root.openSettings()
}

function toggleKiosk(flag) {
  $root.appKiosk(flag)
}

function appClose() {
  $confirm('Close Application?', 'Warning', {
    confirmButtonText: 'OK',
    cancelButtonText: 'Cancel',
    type: 'warning'
  })
    .then(() => {
      $root.appClose()
    })
    .catch(() => {})
}

function pcShutDown() {
  $confirm('Shut Down PC?', 'Warning', {
    confirmButtonText: 'OK',
    cancelButtonText: 'Cancel',
    type: 'warning'
  })
    .then(() => {
      $root.pcShutDown()
    })
    .catch(() => {})
}

function pcReboot() {
  $confirm('Reboot PC?', 'Warning', {
    confirmButtonText: 'OK',
    cancelButtonText: 'Cancel',
    type: 'warning'
  })
    .then(() => {
      $root.pcReboot()
    })
    .catch(() => {})
}
</script>

<template>

  <el-row>
    <el-col :span="12">
      <el-button type="primary" @click="openConfig"> Open Config </el-button>
      <el-button type="warning" @click="appClose"> Close Application </el-button>
    </el-col>
    <el-col :span="12">
      <el-row justify="end">
        <el-button type="info" @click="pcReboot">
          <el-icon>
            <RefreshLeft />
          </el-icon>
          <span>Reboot</span>
        </el-button>
        
        <el-button type="danger" @click="pcShutDown" :icon="SwitchButton">Shut Down</el-button>
      </el-row>
    </el-col>
  </el-row>

  <el-divider />

  <el-form ref="form" :model="settings" :rules="rules" label-width="120px">
    <el-form-item label="Host" prop="api.host">
      
      <el-input
        v-model="settings.api.host"        
      >
        <template #prepend>
          <el-select v-model="settings.api.https" style="width: 115px">
            <el-option label="http://" :value="false" />
            <el-option label="https://" :value="true" />
          </el-select>
        </template>
      </el-input>

    </el-form-item>
    <el-form-item label="App Path" prop="api.path">
      <el-input v-model="settings.api.path" />
    </el-form-item>

    <el-divider border-style="dashed" />
    <el-row>
      <el-col :span="6">
        <el-form-item label="Auto Update" prop="autoupdate.enabled">
          <el-switch v-model="settings.autoupdate.enabled" />
        </el-form-item>
      </el-col>
      <el-col :span="4">
        <el-button type="success" @click="checkUpdate"> Check Now </el-button>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-form-item label="URL" prop="autoupdate.url">
          <el-input v-model="settings.autoupdate.url" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="8">
        <el-form-item label="Channel" prop="autoupdate.channel">
          <el-select
            v-model="settings.autoupdate.channel"
            placeholder="Select"
          >
            <el-option
              v-for="item in channel_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>        
      </el-col>

      <el-col :span="8">
        <el-form-item label="Every" prop="autoupdate.interval" :rules="rules.interval">
          <el-col :span="8">
            <el-input v-model.number="settings.autoupdate.interval" />
          </el-col>
          <el-col :span="12" :offset="1"> Minutes </el-col>
        </el-form-item>
      </el-col>      
    </el-row>

    <el-divider border-style="dashed"/>

    <el-row>
      <el-form-item label="Kiosk Mode" prop="kiosk">
        <el-switch v-model="kiosk" @change="toggleKiosk" />
      </el-form-item>
    </el-row>
    <el-row>
      <el-form-item label="Clock" prop="clock.enabled">
        <el-switch v-model="settings.clock.enabled" />
      </el-form-item>
    </el-row>
    <el-form-item>
      <el-button type="success" @click="onSubmit"> Save </el-button>      
    </el-form-item>
  </el-form>
</template>
