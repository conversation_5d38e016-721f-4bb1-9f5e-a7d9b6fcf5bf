import { defineStore } from 'pinia'

export const useUpdaterStore = defineStore('Updater', {
  state: () => {
    return {
      enabled: false,
      interval: 0,
      callback: null,
      cron: null,
      started: false,
      isPaused: false,
      isBackground: false,

      isVisible: false,      
      progress: 0,
      
      isError: false,
      errorMessage: null
    }
  },
  getters: {},
  actions: {
    init(config) {
      // Clear any existing intervals first
      this.stop()
      
      this.enabled = config.enabled
      this.interval = config.interval
      this.callback = config.callback
    },
    start() {
      if (this.enabled && this.interval > 0 && this.callback) {
        // Ensure we stop any existing interval
        this.stop()

        try {
          this.cron = setInterval(
            this.callback,
            this.interval * 60 * 1000
          )

          this.started = true
          this.isPaused = false
          this.isBackground = true

          console.log(`Updater started with ${this.interval} minute interval`)
        } catch (error) {
          console.error('Error starting updater:', error)
          this.started = false
        }
      }
    },
    stop() {
      if (this.cron) {
        try {
          clearInterval(this.cron)
          console.log('Updater interval cleared')
        } catch (error) {
          console.error('Error clearing updater interval:', error)
        } finally {
          this.cron = null
        }
      }

      this.started = false

      if (!this.isPaused) {
        this.isBackground = false
      }
    },
    pause() {
      this.isPaused = true
      this.stop()
    },
    visible(visible) {
      this.isVisible = visible

      if (visible === false) this.reset()
    },
    updateProgress(percent) {
      this.progress = Math.floor(percent)
    },
    progressPercent() {
      if (this.progress > 0) return this.progress + '%'
      
      return ''
    },
    progress() {
      return this.progress
    },
    reset() {
      this.isVisible = false      

      this.progress = null

      this.isError = false
      this.errorMessage = null
    },
    markError(message) {
      this.isError = true
      this.errorMessage = message
    },
    cleanup() {
      this.stop()
      this.reset()
      this.enabled = false
      this.interval = 0
      this.callback = null
    },
    handleMessage(data) {
      // Handle updater messages from main process
      switch (data.cmd) {
        case 'checking-for-update':
          console.log('Checking for updates...')
          break
        case 'update-available':
          console.log('Update available:', data.updateInfo)
          break
        case 'update-not-available':
          console.log('No updates available')
          break
        case 'download-progress':
          this.updateProgress(data.progressObj.percent)
          break
        case 'update-downloaded':
          console.log('Update downloaded:', data.message)
          this.visible(true)
          break
        case 'update-error':
          console.error('Update error:', data.message)
          this.markError(data.message)
          break
        default:
          console.log('Unknown updater message:', data)
      }
    }
  }
})
