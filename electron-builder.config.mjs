import pkg from './package.json' with {type: 'json'};

const version = "1.1.2"; // Change here don't touch package.json
const name = pkg.name;
const appId = pkg.appId;
const productName = pkg.productName;
const guid = pkg.guid;
const maintainer = pkg.homepage;

/** @type {import("electron-builder").Configuration} */
export default {
  appId: appId,
  productName: productName,
  extraMetadata: {
    version: version
  },
  // copyright: 'Copyright © 2024',
  directories: {
    buildResources: 'build'
  },
  files: [
    '!**/.vscode/*',
    '!src/*',
    '!electron.vite.config.{js,ts,mjs,cjs}',
    '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}',
    '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  ],
  extraResources: [
    {
      from: 'resources/',
      to: './'
    }
  ],
  asarUnpack: ['resources/**'],
  asar: true,
  win: {
    executableName: name,
    icon: 'build/icons/icon.ico',
    target: [
      {
        target: 'nsis',
        arch: ['x64']
      }
    ]
  },
  nsis: {
    guid: guid,
    oneClick: true,
    allowElevation: true,
    allowToChangeInstallationDirectory: false,
    installerIcon: 'build/icons/icon.ico',
    uninstallerIcon: 'build/icons/icon.ico',
    installerHeaderIcon: 'build/icons/icon.ico',
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    deleteAppDataOnUninstall: false
  },
  mac: {
    entitlementsInherit: 'build/entitlements.mac.plist',
    extendInfo: {
      NSCameraUsageDescription: "Application requests access to the device's camera.",
      NSMicrophoneUsageDescription: "Application requests access to the device's microphone.",
      NSDocumentsFolderUsageDescription: "Application requests access to the user's Documents folder.",
      NSDownloadsFolderUsageDescription: "Application requests access to the user's Downloads folder."
    },
    notarize: false
  },
  dmg: {
    artifactName: '${productName}-${version}.${ext}'
  },
  linux: {
    icon: 'build/icons/icon.png',
    executableName: name,
    target: [
      // 'AppImage',
      'deb'
    ],
    maintainer: maintainer,
    category: 'Utility'
  },
  appImage: {
    artifactName: '${productName}-${version}.${ext}'
  },
  deb: {
    afterRemove: 'build/after-remove.sh'
  },
  npmRebuild: false,
  publish: pkg.publish
};