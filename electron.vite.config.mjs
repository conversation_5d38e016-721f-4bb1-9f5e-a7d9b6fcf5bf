import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import renderer from 'vite-plugin-electron-renderer'

export default defineConfig(({ command }) => {
  const isServe = command === 'serve'
  const isBuild = command === 'build'
  const sourcemap = isServe || !!process.env.VSCODE_DEBUG

  return {
    main: {
      resolve: {
        alias: {
          '@root': resolve(''),
          '@renderer': resolve('src/renderer/src')
        },
        extensions: ['.js', '.json', '.vue']
      },
      plugins: [externalizeDepsPlugin()]
    },
    preload: {
      resolve: {
        alias: {
          '@root': resolve('')
        },
        extensions: ['.js', '.json', '.vue']
      },
      plugins: [externalizeDepsPlugin()]
    },
    renderer: {
      resolve: {
        alias: {
          '@root': resolve(''),
          '@renderer': resolve('src/renderer/src')
        },
        extensions: ['.js', '.json', '.vue']
      },
      plugins: [
        vue({
          template: {
            compilerOptions: {
              isCustomElement: (tag) => tag === 'webview' || tag.startsWith('i-')
            }
          }
        }),
        renderer()
      ]
    }
  }
})
